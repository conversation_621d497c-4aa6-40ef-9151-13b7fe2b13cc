// Reliable proxy test script that works with both native fetch and node-fetch
const { HttpsProxyAgent } = require('https-proxy-agent');

// 设置 TLS 配置
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

// 读取环境变量配置
const ENABLE_PROXY = process.env.ENABLE_HTTP_PROXY === 'true';
const PROXY_URL = process.env.HTTPS_PROXY || process.env.HTTP_PROXY || 'http://127.0.0.1:7890';

console.log('🧪 测试可靠的代理配置...');
console.log(`Node.js 版本: ${process.version}`);

// 尝试使用不同的 fetch 实现
async function getFetch() {
  // 首先尝试原生 fetch
  if (typeof fetch !== 'undefined') {
    console.log('✅ 使用 Node.js 原生 fetch');
    return fetch;
  }
  
  // 回退到 node-fetch
  try {
    const nodeFetch = require('node-fetch');
    console.log('✅ 使用 node-fetch');
    return nodeFetch;
  } catch (error) {
    console.error('❌ 无法加载 node-fetch:', error.message);
    throw new Error('无法找到可用的 fetch 实现');
  }
}

async function testProxy() {
  console.log('');
  console.log('环境变量:');
  console.log(`  NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
  console.log(`  ENABLE_HTTP_PROXY: ${process.env.ENABLE_HTTP_PROXY || 'undefined'}`);
  console.log(`  HTTPS_PROXY: ${process.env.HTTPS_PROXY || 'undefined'}`);
  console.log(`  HTTP_PROXY: ${process.env.HTTP_PROXY || 'undefined'}`);
  console.log('');

  console.log('代理配置:');
  console.log(`  使用代理: ${ENABLE_PROXY ? '是' : '否'}`);
  console.log(`  代理地址: ${PROXY_URL}`);
  console.log('');

  try {
    const fetchImpl = await getFetch();
    const proxyAgent = ENABLE_PROXY ? new HttpsProxyAgent(PROXY_URL) : null;

    console.log('🌐 测试网络连接...');

    const fetchOptions = {
      headers: {
        'User-Agent': 'AI-Resume-Reliable-Test/1.0'
      }
    };

    if (proxyAgent) {
      fetchOptions.agent = proxyAgent;
    }

    const response = await fetchImpl('https://api.openai.com/v1/models', fetchOptions);
    
    console.log(`✅ 连接成功！`);
    console.log(`  状态码: ${response.status}`);
    console.log(`  状态文本: ${response.statusText}`);
    
    if (response.status === 401) {
      console.log('  ℹ️  401 状态码表示连接正常（需要 API 密钥）');
      if (proxyAgent) {
        console.log('  🎯 代理工作正常！');
      }
    }
    
    // 显示响应头信息
    console.log('  响应头信息:');
    const contentType = response.headers.get ? response.headers.get('content-type') : response.headers['content-type'];
    const server = response.headers.get ? response.headers.get('server') : response.headers['server'];
    console.log(`    Content-Type: ${contentType}`);
    console.log(`    Server: ${server}`);
    
  } catch (error) {
    console.error('❌ 连接失败:');
    console.error(`  错误: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('  💡 建议: 检查代理服务器是否启动 (Clash/V2Ray 等)');
      console.error('  💡 检查: 确认代理端口 7890 是否正确');
    } else if (error.code === 'ENOTFOUND') {
      console.error('  💡 建议: 检查网络连接');
    } else if (error.code === 'ETIMEDOUT') {
      console.error('  💡 建议: 代理服务器响应超时，检查代理配置');
    }
    
    console.error('  完整错误:', error);
    return false;
  }

  console.log('');
  console.log('📝 测试完成！');
  console.log('如果看到 "代理工作正常" 消息，说明代理配置成功。');
  console.log('现在可以运行 npm run dev 启动项目。');
  
  return true;
}

// 运行测试
testProxy().catch(error => {
  console.error('测试脚本执行失败:', error);
  process.exit(1);
});
