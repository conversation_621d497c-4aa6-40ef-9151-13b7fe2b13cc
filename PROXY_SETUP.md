# 代理配置说明

## 概述
本项目已集成极简代理方案，**开发环境自动使用代理，生产环境不使用代理**。

## 前置条件
1. Node.js 已安装
2. 代理客户端（如 Clash）已开启
3. 确认代理的 HTTP 端口为 7890（如果不是，请修改 `src/lib/proxy.ts` 中的 `PROXY_URL`）

## 使用方法

### 开发环境（自动使用代理）
```bash
# macOS/Linux
npm run dev

# Windows
npm run dev:win
```

### 生产环境（不使用代理）
```bash
npm run build
npm start
```

## 工作原理

1. **自动检测环境**：只在 `NODE_ENV=development` 时使用代理
2. **统一配置**：所有 AI 服务（OpenAI、Claude、Gemini 等）都会自动使用代理
3. **TLS 处理**：开发环境自动禁用 TLS 证书验证
4. **零配置**：无需手动设置，启动开发服务器即可

## 测试代理连接

运行测试脚本验证代理是否工作：
```bash
NODE_TLS_REJECT_UNAUTHORIZED=0 node test-proxy.js
```

预期输出：
```
正在通过代理 http://127.0.0.1:7890 请求...
✅ 代理成功！服务器响应状态: 401
```

## 自定义代理端口

如果你的代理端口不是 7890，请修改 `src/lib/proxy.ts`：
```typescript
private static readonly PROXY_URL = 'http://127.0.0.1:你的端口';
```

## 故障排除

1. **代理连接失败**：确认 Clash 等代理软件已启动且端口正确
2. **证书错误**：确认使用了正确的启动命令（包含 NODE_TLS_REJECT_UNAUTHORIZED=0）
3. **生产环境意外使用代理**：检查 NODE_ENV 环境变量是否正确设置

## 文件说明

- `src/lib/proxy.ts`：代理配置核心文件
- `src/utils/ai-tools.ts`：AI 客户端配置（已集成代理）
- `test-proxy.js`：代理连接测试脚本
- `package.json`：包含开发环境启动脚本
