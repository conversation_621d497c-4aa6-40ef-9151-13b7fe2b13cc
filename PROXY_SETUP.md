# 代理配置说明

## 概述
本项目已集成极简代理方案，**开发环境自动使用代理，生产环境不使用代理**。

## 前置条件
1. Node.js 已安装
2. 代理客户端（如 Clash）已开启
3. 在 `.env.local` 文件中添加以下配置：
   ```
   ENABLE_HTTP_PROXY=true
   HTTPS_PROXY=http://127.0.0.1:7890
   ```

## 使用方法

### 开发环境（自动使用代理）
```bash
# macOS/Linux
npm run dev

# Windows
npm run dev:win
```

### 生产环境（不使用代理）
```bash
npm run build
npm start
```

## 工作原理

1. **环境变量控制**：通过 `ENABLE_HTTP_PROXY=true` 启用代理
2. **灵活配置**：支持 `HTTPS_PROXY` 和 `HTTP_PROXY` 环境变量
3. **统一配置**：所有 AI 服务（OpenAI、<PERSON>、Gemini 等）都会自动使用代理
4. **TLS 处理**：启用代理时自动禁用 TLS 证书验证
5. **生产安全**：生产环境不会使用代理（除非明确设置）

## 测试代理连接

### 推荐方法：使用可靠测试脚本
```bash
npm run test:proxy
# 或直接运行
node test-proxy-reliable.js
```

### 其他测试方法：
```bash
# 测试原生 Node.js fetch（可能有限制）
npm run test:proxy-native

# 测试原始脚本（ES 模块）
npm run test:proxy-original

# 测试项目配置
npm run test:proxy-config
```

### 预期输出（成功）：
```
🧪 测试可靠的代理配置...
Node.js 版本: v23.11.0
✅ 使用 node-fetch

环境变量:
  ENABLE_HTTP_PROXY: true
  HTTPS_PROXY: http://127.0.0.1:7890

代理配置:
  使用代理: 是
  代理地址: http://127.0.0.1:7890

🌐 测试网络连接...
✅ 连接成功！
  状态码: 401
  ℹ️  401 状态码表示连接正常（需要 API 密钥）
  🎯 代理工作正常！
```

## 自定义代理端口

如果你的代理端口不是 7890，请修改 `.env.local` 文件：
```
ENABLE_HTTP_PROXY=true
HTTPS_PROXY=http://127.0.0.1:你的端口
```

## 故障排除

### 测试脚本问题
1. **"fetch is not a function" 错误**：
   - 使用 `npm run test:proxy`（推荐）
   - 或确保 Node.js 版本 ≥ 18.0.0

2. **"TypeError: fetch is not a function" 持续出现**：
   - Node.js 原生 fetch 支持有限
   - 项目中使用 node-fetch，测试脚本会自动回退

### 代理连接问题
1. **代理连接失败 (ECONNREFUSED)**：
   - 确认 Clash/V2Ray 等代理软件已启动
   - 检查代理端口是否为 7890
   - 尝试在浏览器访问 http://127.0.0.1:7890

2. **证书错误**：
   - 确认环境变量中 `ENABLE_HTTP_PROXY=true`
   - 代理启用时会自动禁用 TLS 验证

3. **生产环境意外使用代理**：
   - 检查 `NODE_ENV` 环境变量
   - 确认 `ENABLE_HTTP_PROXY` 在生产环境中未设置或为 false

## 文件说明

### 核心文件
- `src/lib/proxy.ts`：代理配置核心文件
- `src/utils/ai-tools.ts`：AI 客户端配置（已集成代理）
- `.env.local`：环境变量配置文件（包含详细注释）

### 测试脚本
- `test-proxy-reliable.js`：**推荐** - 可靠的代理测试脚本
- `test-proxy-native.mjs`：原生 Node.js fetch 测试（ES 模块）
- `test-proxy.js`：原始测试脚本（ES 模块版本）
- `test-proxy-config.js`：项目配置测试脚本

### 配置文件
- `package.json`：包含所有测试脚本和开发环境启动命令
- `PROXY_SETUP.md`：详细设置说明
- `verify-setup.md`：验证步骤指南
