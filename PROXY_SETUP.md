# 代理配置说明

## 概述
本项目已集成极简代理方案，**开发环境自动使用代理，生产环境不使用代理**。

## 前置条件
1. Node.js 已安装
2. 代理客户端（如 Clash）已开启
3. 在 `.env.local` 文件中添加以下配置：
   ```
   ENABLE_HTTP_PROXY=true
   HTTPS_PROXY=http://127.0.0.1:7890
   ```

## 使用方法

### 开发环境（自动使用代理）
```bash
# macOS/Linux
npm run dev

# Windows
npm run dev:win
```

### 生产环境（不使用代理）
```bash
npm run build
npm start
```

## 工作原理

1. **环境变量控制**：通过 `ENABLE_HTTP_PROXY=true` 启用代理
2. **灵活配置**：支持 `HTTPS_PROXY` 和 `HTTP_PROXY` 环境变量
3. **统一配置**：所有 AI 服务（OpenAI、<PERSON>、Gemini 等）都会自动使用代理
4. **TLS 处理**：启用代理时自动禁用 TLS 证书验证
5. **生产安全**：生产环境不会使用代理（除非明确设置）

## 测试代理连接

### 方法1：使用原始测试脚本
```bash
node test-proxy.js
```

### 方法2：使用项目配置测试脚本
```bash
node test-proxy-config.js
```

预期输出：
```
🧪 测试代理配置...
环境变量:
  ENABLE_HTTP_PROXY: true
  HTTPS_PROXY: http://127.0.0.1:7890
🌐 使用代理: http://127.0.0.1:7890
✅ 连接成功！
  状态码: 401
  ℹ️  401 状态码表示代理工作正常（需要 API 密钥）
```

## 自定义代理端口

如果你的代理端口不是 7890，请修改 `.env.local` 文件：
```
ENABLE_HTTP_PROXY=true
HTTPS_PROXY=http://127.0.0.1:你的端口
```

## 故障排除

1. **代理连接失败**：确认 Clash 等代理软件已启动且端口正确
2. **证书错误**：确认使用了正确的启动命令（包含 NODE_TLS_REJECT_UNAUTHORIZED=0）
3. **生产环境意外使用代理**：检查 NODE_ENV 环境变量是否正确设置

## 文件说明

- `src/lib/proxy.ts`：代理配置核心文件
- `src/utils/ai-tools.ts`：AI 客户端配置（已集成代理）
- `test-proxy.js`：简单代理连接测试脚本
- `test-proxy-config.js`：使用项目配置的测试脚本
- `package.json`：包含开发环境启动脚本
- `.env.local`：环境变量配置文件
