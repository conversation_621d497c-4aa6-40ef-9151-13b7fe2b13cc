// Use dynamic import for ES modules and native fetch
import { HttpsProxyAgent } from 'https-proxy-agent';

// 设置 TLS 配置
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

// 1. 配置你的代理服务器地址
const PROXY_URL = 'http://127.0.0.1:7890'; // <-- 关键: 确保端口正确

// 2. 创建一个代理 agent 实例
const proxyAgent = new HttpsProxyAgent(PROXY_URL);

// 3. 发起请求
console.log(`正在通过代理 ${PROXY_URL} 请求...`);
console.log(`Node.js 版本: ${process.version}`);
console.log(`fetch 可用: ${typeof fetch !== 'undefined' ? '是' : '否'}`);

try {
  const response = await fetch('https://api.openai.com/v1/models', {
    // @ts-ignore - Node.js native fetch supports agent in newer versions
    agent: proxyAgent, // <-- 关键: 在请求中指定使用代理
    headers: {
      'User-Agent': 'AI-Resume-Proxy-Test/1.0'
    }
  });

  // 只要收到服务器响应（无论状态码是多少），就代表代理已成功工作
  console.log(`✅ 代理成功！服务器响应状态: ${response.status}`);
  console.log(`状态文本: ${response.statusText}`);

  if (response.status === 401) {
    console.log('ℹ️  401 状态码表示代理工作正常（需要 API 密钥）');
  }

  // 显示一些响应头信息
  console.log('响应头信息:');
  console.log(`  Content-Type: ${response.headers.get('content-type')}`);
  console.log(`  Server: ${response.headers.get('server')}`);

} catch (err) {
  console.error('❌ 代理失败:', err.message);

  if (err.code === 'ECONNREFUSED') {
    console.error('💡 建议: 检查代理服务器是否启动 (Clash/V2Ray 等)');
  } else if (err.code === 'ENOTFOUND') {
    console.error('💡 建议: 检查网络连接');
  } else if (err.message.includes('agent')) {
    console.error('💡 建议: Node.js 版本可能不支持 agent 参数，尝试使用 node-fetch');
  }

  console.error('完整错误信息:', err);
}
