const fetch = require('node-fetch');
const { HttpsProxyAgent } = require('https-proxy-agent');

// 1. 配置你的代理服务器地址
const PROXY_URL = 'http://127.0.0.1:7890'; // <-- 关键: 确保端口正确

// 2. 创建一个代理 agent 实例
const proxyAgent = new HttpsProxyAgent(PROXY_URL);

// 3. 发起请求
console.log(`正在通过代理 ${PROXY_URL} 请求...`);

fetch('https://api.openai.com/v1/models', {
  agent: proxyAgent, // <-- 关键: 在请求中指定使用代理
})
.then(response => {
  // 只要收到服务器响应（无论状态码是多少），就代表代理已成功工作
  console.log(`✅ 代理成功！服务器响应状态: ${response.status}`);
})
.catch(err => {
  console.error('❌ 代理失败:', err.message);
});
