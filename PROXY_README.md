# Proxy Configuration - Quick Reference

## Setup (One-time)
1. Add to `.env.local`:
   ```
   ENABLE_HTTP_PROXY=true
   HTTPS_PROXY=http://127.0.0.1:7890
   ```

2. Start your proxy software (Clash/V2Ray)

## Test Proxy
```bash
npm run test:proxy
```

## Start Development
```bash
npm run dev
```

## Files
- `proxy-test.js` - Test script with full documentation in comments
- `src/lib/proxy.ts` - Proxy configuration for the app
- `.env.local` - Environment variables (with detailed comments)

All detailed instructions and troubleshooting are in the `proxy-test.js` file comments.
