# 验证代理设置

## 1. 检查环境变量配置

确认你的 `.env.local` 文件包含：
```
ENABLE_HTTP_PROXY=true
HTTPS_PROXY=http://127.0.0.1:7890
```

## 2. 测试代理连接

### 方法1：测试基础代理功能
```bash
node test-proxy.js
```

预期输出：
```
正在通过代理 http://127.0.0.1:7890 请求...
✅ 代理成功！服务器响应状态: 401
```

### 方法2：测试项目配置
```bash
node test-proxy-config.js
```

预期输出：
```
🧪 测试代理配置...
环境变量:
  ENABLE_HTTP_PROXY: true
  HTTPS_PROXY: http://127.0.0.1:7890
🌐 使用代理: http://127.0.0.1:7890
✅ 连接成功！
  状态码: 401
```

## 3. 启动开发服务器

```bash
npm run dev
```

启动时应该看到类似输出：
```
🌐 使用代理: http://127.0.0.1:7890 (NODE_ENV: development)
🔓 已禁用 TLS 证书验证（代理模式）
```

## 4. 验证 AI 功能

1. 打开浏览器访问你的应用
2. 尝试使用任何 AI 功能（聊天、简历生成等）
3. 检查浏览器开发者工具的网络面板，确认请求成功

## 故障排除

### 如果测试脚本报错 "fetch is not a function"
这是正常的，因为 Node.js 版本问题。项目中的 Next.js 环境会正常工作。

### 如果代理连接失败
1. 确认 Clash 或其他代理软件正在运行
2. 确认代理端口是 7890（或修改配置文件中的端口）
3. 尝试在浏览器中访问 http://127.0.0.1:7890 确认代理服务可用

### 如果 AI 功能不工作
1. 检查浏览器控制台是否有错误信息
2. 确认 API 密钥配置正确
3. 检查网络面板中的请求是否通过代理发送
