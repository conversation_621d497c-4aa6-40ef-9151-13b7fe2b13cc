# 验证代理设置

## 1. 检查环境变量配置

确认你的 `.env.local` 文件包含：
```
ENABLE_HTTP_PROXY=true
HTTPS_PROXY=http://127.0.0.1:7890
```

## 2. 测试代理连接

### 简单测试方法：
```bash
npm run test:proxy
```

预期输出：
```
🧪 Testing proxy configuration...

Environment variables:
  ENABLE_HTTP_PROXY: true
  HTTPS_PROXY: http://127.0.0.1:7890

🌐 Testing connection through proxy: http://127.0.0.1:7890
✅ Connection successful!
   Status: 401 Unauthorized
   ℹ️  Status 401 means proxy is working (API key required)
   🎯 Proxy configuration is working correctly!
```

## 3. 启动开发服务器

```bash
npm run dev
```

启动时应该看到类似输出：
```
🌐 使用代理: http://127.0.0.1:7890 (NODE_ENV: development)
🔓 已禁用 TLS 证书验证（代理模式）
```

## 4. 验证 AI 功能

1. 打开浏览器访问你的应用
2. 尝试使用任何 AI 功能（聊天、简历生成等）
3. 检查浏览器开发者工具的网络面板，确认请求成功

## 故障排除

### 如果测试脚本报错
1. **环境变量问题**：测试脚本会自动从 `.env.local` 加载配置
2. **依赖问题**：确保运行了 `npm install` 安装所有依赖
3. **如果仍然失败**：直接运行 `npm run dev` 测试实际应用

### 如果代理连接失败 (ECONNREFUSED)
1. 确认 Clash 或其他代理软件正在运行
2. 确认代理端口是 7890（或修改 `.env.local` 中的端口）
3. 尝试在浏览器中访问 http://127.0.0.1:7890 确认代理服务可用
4. 检查防火墙设置是否阻止了代理连接

### 如果 AI 功能不工作
1. 检查浏览器控制台是否有错误信息
2. 确认 API 密钥配置正确
3. 检查网络面板中的请求是否通过代理发送
4. 确认 `.env.local` 中的 `ENABLE_HTTP_PROXY=true`

### Node.js 版本相关问题
- **当前项目要求**：Node.js ≥ 20.0.0
- **你的版本**：v23.11.0 ✅ 兼容
- **原生 fetch 支持**：v18+ 有原生 fetch，但代理支持有限
- **项目解决方案**：使用 node-fetch + AI SDK 自定义 fetch 实现
