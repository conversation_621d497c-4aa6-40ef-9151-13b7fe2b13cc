# 验证代理设置

## 1. 检查环境变量配置

确认你的 `.env.local` 文件包含：
```
ENABLE_HTTP_PROXY=true
HTTPS_PROXY=http://127.0.0.1:7890
```

## 2. 测试代理连接

### 推荐方法：使用可靠测试脚本
```bash
npm run test:proxy
```

预期输出：
```
🧪 测试可靠的代理配置...
Node.js 版本: v23.11.0
✅ 使用 node-fetch

环境变量:
  ENABLE_HTTP_PROXY: true
  HTTPS_PROXY: http://127.0.0.1:7890

🌐 测试网络连接...
✅ 连接成功！
  状态码: 401
  🎯 代理工作正常！
```

### 其他测试方法：
```bash
# 如果上面的方法失败，尝试这些：
npm run test:proxy-native    # 原生 Node.js fetch
npm run test:proxy-config    # 项目配置测试
npm run test:proxy-original  # 原始测试脚本
```

## 3. 启动开发服务器

```bash
npm run dev
```

启动时应该看到类似输出：
```
🌐 使用代理: http://127.0.0.1:7890 (NODE_ENV: development)
🔓 已禁用 TLS 证书验证（代理模式）
```

## 4. 验证 AI 功能

1. 打开浏览器访问你的应用
2. 尝试使用任何 AI 功能（聊天、简历生成等）
3. 检查浏览器开发者工具的网络面板，确认请求成功

## 故障排除

### 如果测试脚本报错 "fetch is not a function"
1. **首先尝试**：`npm run test:proxy`（推荐的可靠测试脚本）
2. **如果仍然失败**：Node.js 版本问题，但项目中的 Next.js 环境会正常工作
3. **解决方案**：直接运行 `npm run dev` 测试实际应用

### 如果代理连接失败 (ECONNREFUSED)
1. 确认 Clash 或其他代理软件正在运行
2. 确认代理端口是 7890（或修改 `.env.local` 中的端口）
3. 尝试在浏览器中访问 http://127.0.0.1:7890 确认代理服务可用
4. 检查防火墙设置是否阻止了代理连接

### 如果 AI 功能不工作
1. 检查浏览器控制台是否有错误信息
2. 确认 API 密钥配置正确
3. 检查网络面板中的请求是否通过代理发送
4. 确认 `.env.local` 中的 `ENABLE_HTTP_PROXY=true`

### Node.js 版本相关问题
- **当前项目要求**：Node.js ≥ 20.0.0
- **你的版本**：v23.11.0 ✅ 兼容
- **原生 fetch 支持**：v18+ 有原生 fetch，但代理支持有限
- **项目解决方案**：使用 node-fetch + AI SDK 自定义 fetch 实现
