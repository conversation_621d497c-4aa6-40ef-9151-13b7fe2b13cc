// Native ES Module test script for proxy configuration
import { HttpsProxyAgent } from 'https-proxy-agent';

// 设置 TLS 配置
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

// 读取环境变量配置
const ENABLE_PROXY = process.env.ENABLE_HTTP_PROXY === 'true';
const PROXY_URL = process.env.HTTPS_PROXY || process.env.HTTP_PROXY || 'http://127.0.0.1:7890';

console.log('🧪 测试原生 Node.js fetch 代理配置...');
console.log(`Node.js 版本: ${process.version}`);
console.log(`fetch 可用: ${typeof fetch !== 'undefined' ? '是' : '否'}`);
console.log('');

console.log('环境变量:');
console.log(`  NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
console.log(`  ENABLE_HTTP_PROXY: ${process.env.ENABLE_HTTP_PROXY || 'undefined'}`);
console.log(`  HTTPS_PROXY: ${process.env.HTTPS_PROXY || 'undefined'}`);
console.log(`  HTTP_PROXY: ${process.env.HTTP_PROXY || 'undefined'}`);
console.log('');

console.log('代理配置:');
console.log(`  使用代理: ${ENABLE_PROXY ? '是' : '否'}`);
console.log(`  代理地址: ${PROXY_URL}`);
console.log('');

// 检查 fetch 是否可用
if (typeof fetch === 'undefined') {
  console.error('❌ 错误: 当前 Node.js 版本不支持原生 fetch');
  console.error('💡 建议: 升级到 Node.js 18+ 或使用 node-fetch');
  process.exit(1);
}

// 创建代理 agent
const proxyAgent = ENABLE_PROXY ? new HttpsProxyAgent(PROXY_URL) : null;

console.log('🌐 测试网络连接...');

try {
  const fetchOptions = {
    headers: {
      'User-Agent': 'AI-Resume-Native-Test/1.0'
    }
  };

  // 注意: Node.js 原生 fetch 可能不支持 agent 参数
  // 这是一个已知限制，但我们可以尝试
  if (proxyAgent) {
    // 尝试使用 agent，但可能不工作
    fetchOptions.agent = proxyAgent;
  }

  const response = await fetch('https://api.openai.com/v1/models', fetchOptions);
  
  console.log(`✅ 连接成功！`);
  console.log(`  状态码: ${response.status}`);
  console.log(`  状态文本: ${response.statusText}`);
  
  if (response.status === 401) {
    console.log('  ℹ️  401 状态码表示连接正常（需要 API 密钥）');
  }
  
  // 显示响应头信息
  console.log('  响应头信息:');
  console.log(`    Content-Type: ${response.headers.get('content-type')}`);
  console.log(`    Server: ${response.headers.get('server')}`);
  
  if (proxyAgent) {
    console.log('  🎯 代理可能已生效（无法完全确认，因为 Node.js 原生 fetch 的限制）');
  }
  
} catch (error) {
  console.error('❌ 连接失败:');
  console.error(`  错误: ${error.message}`);
  
  if (error.code === 'ECONNREFUSED') {
    console.error('  💡 建议: 检查代理服务器是否启动');
  } else if (error.code === 'ENOTFOUND') {
    console.error('  💡 建议: 检查网络连接');
  } else if (error.message.includes('agent')) {
    console.error('  💡 说明: Node.js 原生 fetch 不完全支持代理 agent');
    console.error('  💡 建议: 项目中使用 node-fetch 或 AI SDK 的内置代理支持');
  }
  
  console.error('  完整错误:', error);
}

console.log('');
console.log('📝 注意事项:');
console.log('1. Node.js 原生 fetch 对代理支持有限');
console.log('2. 项目中的 AI SDK 使用自定义 fetch 实现，支持代理');
console.log('3. 如果此测试失败，不影响项目中的代理功能');
console.log('4. 请使用 npm run dev 启动项目测试实际代理效果');
