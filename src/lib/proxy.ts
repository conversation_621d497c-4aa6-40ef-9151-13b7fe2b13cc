import { HttpsProxyAgent } from 'https-proxy-agent';

/**
 * 代理配置工具类
 * 开发环境自动使用代理，生产环境不使用
 */
export class ProxyConfig {
  private static readonly PROXY_URL = 'http://127.0.0.1:7890';
  private static proxyAgent: HttpsProxyAgent | null = null;

  /**
   * 获取代理 agent（仅在开发环境）
   */
  static getProxyAgent(): HttpsProxyAgent | undefined {
    // 只在开发环境使用代理
    if (process.env.NODE_ENV !== 'development') {
      return undefined;
    }

    // 懒加载创建代理实例
    if (!this.proxyAgent) {
      this.proxyAgent = new HttpsProxyAgent(this.PROXY_URL);
      console.log(`🌐 开发环境：使用代理 ${this.PROXY_URL}`);
    }

    return this.proxyAgent;
  }

  /**
   * 获取 fetch 配置（包含代理设置）
   */
  static getFetchConfig(): { agent?: HttpsProxyAgent } {
    const agent = this.getProxyAgent();
    return agent ? { agent } : {};
  }

  /**
   * 设置环境变量以忽略 TLS 证书验证（仅开发环境）
   */
  static setupTLSConfig(): void {
    if (process.env.NODE_ENV === 'development') {
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
      console.log('🔓 开发环境：已禁用 TLS 证书验证');
    }
  }
}

// 在模块加载时自动设置 TLS 配置
ProxyConfig.setupTLSConfig();
