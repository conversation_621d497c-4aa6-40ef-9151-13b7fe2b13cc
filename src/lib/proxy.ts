import { HttpsProxyAgent } from 'https-proxy-agent';

/**
 * 代理配置工具类
 * 开发环境自动使用代理，生产环境不使用
 */
export class ProxyConfig {
  private static proxyAgent: HttpsProxyAgent | null = null;

  /**
   * 获取代理 URL
   */
  private static getProxyUrl(): string {
    // 优先使用环境变量中的代理配置
    return process.env.HTTPS_PROXY || process.env.HTTP_PROXY || 'http://127.0.0.1:7890';
  }

  /**
   * 检查是否应该使用代理
   */
  private static shouldUseProxy(): boolean {
    // 检查环境变量是否启用代理
    const enableProxy = process.env.ENABLE_HTTP_PROXY === 'true';
    // 只在开发环境或明确启用代理时使用
    const isDevelopment = process.env.NODE_ENV === 'development';

    return enableProxy || isDevelopment;
  }

  /**
   * 获取代理 agent
   */
  static getProxyAgent(): HttpsProxyAgent | undefined {
    if (!this.shouldUseProxy()) {
      return undefined;
    }

    // 懒加载创建代理实例
    if (!this.proxyAgent) {
      const proxyUrl = this.getProxyUrl();
      this.proxyAgent = new HttpsProxyAgent(proxyUrl);
      console.log(`🌐 使用代理: ${proxyUrl} (NODE_ENV: ${process.env.NODE_ENV})`);
    }

    return this.proxyAgent;
  }

  /**
   * 获取 fetch 配置（包含代理设置）
   */
  static getFetchConfig(): { agent?: HttpsProxyAgent } {
    const agent = this.getProxyAgent();
    return agent ? { agent } : {};
  }

  /**
   * 设置环境变量以忽略 TLS 证书验证
   */
  static setupTLSConfig(): void {
    if (this.shouldUseProxy()) {
      process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
      console.log('🔓 已禁用 TLS 证书验证（代理模式）');
    }
  }

  /**
   * 重置代理配置（用于测试或重新配置）
   */
  static reset(): void {
    this.proxyAgent = null;
  }
}

// 在模块加载时自动设置 TLS 配置
ProxyConfig.setupTLSConfig();
