// 测试项目代理配置
const fetch = require('node-fetch');
const { HttpsProxyAgent } = require('https-proxy-agent');

// 手动设置环境变量（模拟 .env.local）
process.env.ENABLE_HTTP_PROXY = 'true';
process.env.HTTPS_PROXY = 'http://127.0.0.1:7890';
process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';

// 简化的代理配置逻辑
function shouldUseProxy() {
  return process.env.ENABLE_HTTP_PROXY === 'true' || process.env.NODE_ENV === 'development';
}

function getProxyUrl() {
  return process.env.HTTPS_PROXY || process.env.HTTP_PROXY || 'http://127.0.0.1:7890';
}

async function testProxy() {
  console.log('🧪 测试代理配置...');
  console.log('环境变量:');
  console.log(`  NODE_ENV: ${process.env.NODE_ENV}`);
  console.log(`  ENABLE_HTTP_PROXY: ${process.env.ENABLE_HTTP_PROXY}`);
  console.log(`  HTTPS_PROXY: ${process.env.HTTPS_PROXY}`);
  console.log(`  HTTP_PROXY: ${process.env.HTTP_PROXY}`);

  // 获取代理配置
  const useProxy = shouldUseProxy();
  const proxyUrl = getProxyUrl();
  const proxyAgent = useProxy ? new HttpsProxyAgent(proxyUrl) : null;

  console.log('\n代理配置:');
  console.log(`  使用代理: ${useProxy ? '是' : '否'}`);
  if (proxyAgent) {
    console.log(`  代理地址: ${proxyUrl}`);
  }
  
  console.log('\n🌐 测试网络连接...');

  try {
    const fetchOptions = {
      headers: {
        'User-Agent': 'AI-Resume-Test/1.0'
      }
    };

    if (proxyAgent) {
      fetchOptions.agent = proxyAgent;
    }

    const response = await fetch('https://api.openai.com/v1/models', fetchOptions);
    
    console.log(`✅ 连接成功！`);
    console.log(`  状态码: ${response.status}`);
    console.log(`  状态文本: ${response.statusText}`);
    
    if (response.status === 401) {
      console.log('  ℹ️  401 状态码表示代理工作正常（需要 API 密钥）');
    }
    
  } catch (error) {
    console.error('❌ 连接失败:');
    console.error(`  错误: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('  💡 建议: 检查代理服务器是否启动');
    } else if (error.code === 'ENOTFOUND') {
      console.error('  💡 建议: 检查网络连接');
    }
  }
}

// 运行测试
testProxy().catch(console.error);
