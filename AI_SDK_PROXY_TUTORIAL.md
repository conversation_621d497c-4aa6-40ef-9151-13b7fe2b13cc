# AI SDK 代理配置教程

## 背景说明

AI SDK（如 @ai-sdk/openai, @ai-sdk/anthropic 等）使用自定义的 fetch 实现，需要特殊的代理配置方式。

## 核心区别

### 普通 Node.js 项目（你的教程）
```javascript
// 使用 Node.js 原生 fetch + dispatcher
const { HttpsProxyAgent } = require('https-proxy-agent');
const proxyAgent = new HttpsProxyAgent('http://127.0.0.1:7890');

fetch('https://api.openai.com/v1/models', {
  dispatcher: proxyAgent  // Node.js 原生 fetch 用法
});
```

### AI SDK 项目（本项目方法）
```javascript
// 使用 node-fetch + agent，然后传给 AI SDK
const fetch = require('node-fetch');
const { HttpsProxyAgent } = require('https-proxy-agent');
const proxyAgent = new HttpsProxyAgent('http://127.0.0.1:7890');

// AI SDK 配置
const openai = createOpenAI({
  apiKey: 'your-key',
  fetch: (url, init) => fetch(url, { ...init, agent: proxyAgent })
});
```

## 完整实现步骤

### 1. 安装依赖
```bash
pnpm install node-fetch https-proxy-agent
```

### 2. 环境变量配置（.env.local）
```
ENABLE_HTTP_PROXY=true
HTTPS_PROXY=http://127.0.0.1:7890
```

### 3. 代理配置类（src/lib/proxy.ts）
```typescript
import { HttpsProxyAgent } from 'https-proxy-agent';

export class ProxyConfig {
  private static proxyAgent: HttpsProxyAgent<string> | null = null;

  static shouldUseProxy(): boolean {
    return process.env.ENABLE_HTTP_PROXY === 'true' || 
           process.env.NODE_ENV === 'development';
  }

  static getProxyAgent(): HttpsProxyAgent<string> | undefined {
    if (!this.shouldUseProxy()) return undefined;

    if (!this.proxyAgent) {
      const proxyUrl = process.env.HTTPS_PROXY || 'http://127.0.0.1:7890';
      this.proxyAgent = new HttpsProxyAgent(proxyUrl);
    }
    return this.proxyAgent;
  }

  static getFetchConfig() {
    const proxyAgent = this.getProxyAgent();
    return proxyAgent ? { agent: proxyAgent } : {};
  }
}
```

### 4. AI 客户端配置（src/utils/ai-tools.ts）
```typescript
import { createOpenAI } from '@ai-sdk/openai';
import { ProxyConfig } from '@/lib/proxy';

// 动态导入 node-fetch
const getNodeFetch = async () => {
  const nodeFetch = await import('node-fetch');
  return nodeFetch.default;
};

export function initializeAIClient() {
  const proxyConfig = ProxyConfig.getFetchConfig();
  
  return createOpenAI({
    apiKey: process.env.OPENAI_API_KEY,
    fetch: async (url, init) => {
      const fetch = await getNodeFetch();
      return fetch(url, { ...init, ...proxyConfig });
    }
  });
}
```

### 5. 测试脚本
```javascript
// proxy-test.js
const fetch = require('node-fetch');
const { HttpsProxyAgent } = require('https-proxy-agent');

const proxyAgent = new HttpsProxyAgent('http://127.0.0.1:7890');

fetch('https://api.openai.com/v1/models', {
  agent: proxyAgent  // node-fetch 用法
})
.then(res => console.log(`✅ 代理成功！状态: ${res.status}`))
.catch(err => console.error('❌ 代理失败:', err.message));
```

## 关键差异总结

| 方面 | 普通 Node.js | AI SDK 项目 |
|------|-------------|-------------|
| fetch 库 | 原生 fetch | node-fetch |
| 代理参数 | `dispatcher` | `agent` |
| 配置方式 | 直接使用 | 通过 AI SDK 的 fetch 参数 |
| 依赖 | 仅 https-proxy-agent | node-fetch + https-proxy-agent |

## 为什么需要这样做？

1. **AI SDK 限制**：AI SDK 通过 `fetch` 参数接受自定义 fetch 函数
2. **兼容性**：node-fetch 对代理的支持更成熟稳定
3. **统一性**：所有 AI 服务都能使用相同的代理配置

这就是为什么你的教程方法在 AI SDK 项目中不直接适用的原因。
