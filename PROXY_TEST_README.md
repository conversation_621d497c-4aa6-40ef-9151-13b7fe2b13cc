# Proxy Test - Quick Start

## Simple Test Command
```bash
npm run test:proxy
```

## What the test does:
1. ✅ Loads environment variables from `.env.local`
2. ✅ Checks if proxy is enabled (`ENABLE_HTTP_PROXY=true`)
3. ✅ Verifies proxy URL is configured (`HTTPS_PROXY=http://127.0.0.1:7890`)
4. ✅ Tests connection to OpenAI API through the proxy
5. ✅ Reports success/failure with troubleshooting tips

## Expected Success Output:
```
🧪 Testing proxy configuration...

Environment variables:
  ENABLE_HTTP_PROXY: true
  HTTPS_PROXY: http://127.0.0.1:7890

🌐 Testing connection through proxy: http://127.0.0.1:7890
✅ Connection successful!
   Status: 401 Unauthorized
   ℹ️  Status 401 means proxy is working (API key required)
   🎯 Proxy configuration is working correctly!
```

## Common Issues:

### ETIMEDOUT Error
- Check if Clash/V2Ray is running
- Restart your proxy software
- Verify port 7890 is correct

### ECONNREFUSED Error  
- Proxy software is not running
- Wrong port number
- Try accessing http://127.0.0.1:7890 in browser

### Environment Variables "undefined"
- Make sure `.env.local` exists in project root
- Check file contains `ENABLE_HTTP_PROXY=true` and `HTTPS_PROXY=http://127.0.0.1:7890`

## Next Steps:
If test passes, run `npm run dev` to start the application with proxy support.
