/**
 * 简单代理测试 - 验证代理服务器是否运行
 * 运行: node simple-proxy-test.js
 */

const net = require('net');

async function testProxyServer() {
  console.log('🔍 Testing if proxy server is running on port 7890...\n');
  
  const socket = new net.Socket();
  
  try {
    await new Promise((resolve, reject) => {
      socket.setTimeout(5000);
      
      socket.on('connect', () => {
        console.log('✅ SUCCESS: Proxy server is running on 127.0.0.1:7890');
        console.log('🎯 Your proxy software (Clash/V2Ray) is working!');
        socket.destroy();
        resolve();
      });
      
      socket.on('timeout', () => {
        socket.destroy();
        reject(new Error('Connection timeout'));
      });
      
      socket.on('error', (err) => {
        socket.destroy();
        reject(err);
      });
      
      socket.connect(7890, '127.0.0.1');
    });
    
  } catch (error) {
    console.log('❌ FAILED: Cannot connect to proxy server');
    console.log(`   Error: ${error.message}`);
    console.log('\n💡 SOLUTIONS:');
    console.log('   1. Start your proxy software (Clash/V2Ray/etc.)');
    console.log('   2. Make sure it\'s configured to listen on port 7890');
    console.log('   3. Check if another application is using port 7890');
    console.log('   4. Try restarting your proxy software');
    console.log('\n🔧 HOW TO CHECK:');
    console.log('   - Open your proxy software settings');
    console.log('   - Look for "HTTP Port" or "Local Port" setting');
    console.log('   - Make sure it shows 7890');
    console.log('   - If different, update HTTPS_PROXY in .env.local');
  }
}

testProxyServer();
