/**
 * ============================================================================
 * PROXY TEST SCRIPT - AI Resume Project
 * ============================================================================
 *
 * QUICK START:
 * 1. Make sure your proxy software (Clash/V2Ray) is running on port 7890
 * 2. Run: npm run test:proxy
 * 3. Look for "🎯 Proxy configuration is working correctly!" message
 *
 * SETUP REQUIREMENTS:
 * - Add these lines to your .env.local file:
 *   ENABLE_HTTP_PROXY=true
 *   HTTPS_PROXY=http://127.0.0.1:7890
 *
 * WHAT THIS TEST DOES:
 * ✅ Loads environment variables from .env.local automatically
 * ✅ Checks if proxy is enabled and configured
 * ✅ Tests connection to OpenAI API through your proxy
 * ✅ Shows clear success/failure messages with troubleshooting tips
 *
 * EXPECTED SUCCESS OUTPUT:
 * 🧪 Testing proxy configuration...
 * Environment variables:
 *   ENABLE_HTTP_PROXY: true
 *   HTTPS_PROXY: http://127.0.0.1:7890
 * 🌐 Testing connection through proxy: http://127.0.0.1:7890
 * ✅ Connection successful!
 *    Status: 401 Unauthorized
 *    ℹ️  Status 401 means proxy is working (API key required)
 *    🎯 Proxy configuration is working correctly!
 *
 * TROUBLESHOOTING COMMON ERRORS:
 *
 * 1. ETIMEDOUT Error:
 *    - Proxy server is slow or unresponsive
 *    - Solution: Restart your proxy software (Clash/V2Ray)
 *    - Check: Proxy software is actually running and accessible
 *
 * 2. ECONNREFUSED Error:
 *    - Proxy is not running or wrong port
 *    - Solution: Start Clash/V2Ray, verify port 7890 is correct
 *    - Test: Try accessing http://127.0.0.1:7890 in your browser
 *
 * 3. Environment Variables "undefined":
 *    - .env.local file missing or incorrect format
 *    - Solution: Create .env.local in project root with correct values
 *    - Check: File should contain ENABLE_HTTP_PROXY=true (no quotes)
 *
 * 4. "fetch is not a function" Error:
 *    - Node.js version too old
 *    - Solution: This script uses Node.js native fetch (v18+)
 *    - Note: Uses 'dispatcher' option for proxy (not 'agent')
 *
 * PROXY SOFTWARE SETUP:
 * - Clash: Usually runs on port 7890 by default
 * - V2Ray: May use port 10809, update HTTPS_PROXY if different
 * - Other: Check your proxy software's HTTP port setting
 *
 * NEXT STEPS AFTER SUCCESS:
 * 1. Run "npm run dev" to start the development server
 * 2. All AI API calls will automatically use the proxy
 * 3. Check browser console for any remaining connection issues
 * ============================================================================
 */

const fs = require('fs');
const path = require('path');
const { HttpsProxyAgent } = require('https-proxy-agent');

// Node.js v23.11.0 has native fetch, let's use it
// If not available, we'll handle it in the async function

// Load environment variables from .env.local
function loadEnvFile() {
  const envPath = path.join(__dirname, '.env.local');
  
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env.local file not found');
    process.exit(1);
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const lines = envContent.split('\n');

  lines.forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#') && line.includes('=')) {
      const [key, ...valueParts] = line.split('=');
      const value = valueParts.join('=').replace(/^["']|["']$/g, ''); // Remove quotes
      process.env[key] = value;
    }
  });
}

async function testProxy() {
  console.log('🧪 Testing proxy configuration...\n');

  // 使用 Node.js 原生 fetch (v18+)
  if (typeof fetch === 'undefined') {
    console.error('❌ Node.js 原生 fetch 不可用');
    console.error('💡 请升级到 Node.js 18+ 版本');
    process.exit(1);
  }
  console.log('✅ 使用 Node.js 原生 fetch (按照教程方法)\n');

  // Load environment variables
  loadEnvFile();

  // Check environment variables
  const enableProxy = process.env.ENABLE_HTTP_PROXY;
  const proxyUrl = process.env.HTTPS_PROXY;

  console.log('Environment variables:');
  console.log(`  ENABLE_HTTP_PROXY: ${enableProxy}`);
  console.log(`  HTTPS_PROXY: ${proxyUrl}\n`);

  if (enableProxy !== 'true') {
    console.log('❌ Proxy is disabled (ENABLE_HTTP_PROXY is not "true")');
    console.log('💡 Set ENABLE_HTTP_PROXY=true in .env.local to enable proxy');
    return;
  }

  if (!proxyUrl) {
    console.log('❌ No proxy URL configured');
    console.log('💡 Set HTTPS_PROXY=http://127.0.0.1:7890 in .env.local');
    return;
  }

  // Set up proxy
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
  const proxyAgent = new HttpsProxyAgent(proxyUrl);

  console.log(`🌐 Testing connection through proxy: ${proxyUrl}`);
  try {
    // 按照教程的确切方式发起请求 - Node.js 原生 fetch 使用 dispatcher
    const response = await fetch('https://api.openai.com/v1/models', {
      dispatcher: proxyAgent, // <-- 关键: Node.js 原生 fetch 使用 dispatcher
      headers: {
        'User-Agent': 'AI-Resume-Proxy-Test/1.0'
      }
    });

    // 只要收到服务器响应（无论状态码是多少），就代表代理已成功工作
    console.log(`✅ 代理成功！服务器响应状态: ${response.status}`);

    if (response.status === 401) {
      console.log('   ℹ️  401 状态码表示代理工作正常（需要 API 密钥）');
      console.log('   🎯 代理配置工作正常！');
    } else if (response.status === 200) {
      console.log('   🎯 代理配置工作正常！');
    } else {
      console.log(`   ⚠️  意外的状态码: ${response.status}`);
      console.log('   但这仍然表示代理连接成功');
    }

  } catch (err) {
    console.error('❌ 代理失败:', err.message);
    console.error('错误详情:', err);

    console.log('\n💡 故障排除:');
    if (err.code === 'ECONNREFUSED') {
      console.log('   1. 确认 Clash 或其他代理软件正在运行');
      console.log('   2. 检查代理是否在端口 7890 上监听');
      console.log('   3. 尝试在浏览器中访问 http://127.0.0.1:7890');
    } else if (err.code === 'ETIMEDOUT') {
      console.log('   1. 代理服务器可能响应缓慢或无响应');
      console.log('   2. 检查代理软件设置');
      console.log('   3. 尝试重启代理软件');
    } else {
      console.log('   1. 检查网络连接');
      console.log('   2. 验证代理服务器地址是否正确');
      console.log('   3. 确认代理软件配置正确');
    }
  }
}

// Run the test
testProxy().catch(error => {
  console.error('Test failed:', error.message);
  process.exit(1);
});
