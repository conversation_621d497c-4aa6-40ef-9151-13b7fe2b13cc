const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');
const { HttpsProxyAgent } = require('https-proxy-agent');

// Load environment variables from .env.local
function loadEnvFile() {
  const envPath = path.join(__dirname, '.env.local');
  
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env.local file not found');
    process.exit(1);
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const lines = envContent.split('\n');

  lines.forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#') && line.includes('=')) {
      const [key, ...valueParts] = line.split('=');
      const value = valueParts.join('=').replace(/^["']|["']$/g, ''); // Remove quotes
      process.env[key] = value;
    }
  });
}

async function testProxy() {
  console.log('🧪 Testing proxy configuration...\n');

  // Load environment variables
  loadEnvFile();

  // Check environment variables
  const enableProxy = process.env.ENABLE_HTTP_PROXY;
  const proxyUrl = process.env.HTTPS_PROXY;

  console.log('Environment variables:');
  console.log(`  ENABLE_HTTP_PROXY: ${enableProxy}`);
  console.log(`  HTTPS_PROXY: ${proxyUrl}\n`);

  if (enableProxy !== 'true') {
    console.log('❌ Proxy is disabled (ENABLE_HTTP_PROXY is not "true")');
    console.log('💡 Set ENABLE_HTTP_PROXY=true in .env.local to enable proxy');
    return;
  }

  if (!proxyUrl) {
    console.log('❌ No proxy URL configured');
    console.log('💡 Set HTTPS_PROXY=http://127.0.0.1:7890 in .env.local');
    return;
  }

  // Set up proxy
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
  const proxyAgent = new HttpsProxyAgent(proxyUrl);

  console.log(`🌐 Testing connection through proxy: ${proxyUrl}`);

  try {
    const response = await fetch('https://api.openai.com/v1/models', {
      agent: proxyAgent,
      headers: {
        'User-Agent': 'AI-Resume-Proxy-Test/1.0'
      },
      timeout: 10000 // 10 second timeout
    });

    console.log(`✅ Connection successful!`);
    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    if (response.status === 401) {
      console.log('   ℹ️  Status 401 means proxy is working (API key required)');
      console.log('   🎯 Proxy configuration is working correctly!');
    } else if (response.status === 200) {
      console.log('   🎯 Proxy configuration is working correctly!');
    } else {
      console.log(`   ⚠️  Unexpected status code: ${response.status}`);
    }

  } catch (error) {
    console.log('❌ Connection failed:');
    console.log(`   Error: ${error.message}`);

    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Troubleshooting:');
      console.log('   1. Make sure your proxy software (Clash/V2Ray) is running');
      console.log('   2. Check if proxy is listening on port 7890');
      console.log('   3. Try accessing http://127.0.0.1:7890 in your browser');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('\n💡 Troubleshooting:');
      console.log('   1. Proxy server might be slow or unresponsive');
      console.log('   2. Check your proxy software settings');
      console.log('   3. Try restarting your proxy software');
    } else if (error.code === 'ENOTFOUND') {
      console.log('\n💡 Troubleshooting:');
      console.log('   1. Check your internet connection');
      console.log('   2. Verify proxy server address is correct');
    }
  }
}

// Run the test
testProxy().catch(error => {
  console.error('Test failed:', error.message);
  process.exit(1);
});
