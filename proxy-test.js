/**
 * ============================================================================
 * PROXY TEST SCRIPT - AI Resume Project
 * ============================================================================
 *
 * QUICK START:
 * 1. Make sure your proxy software (Clash/V2Ray) is running on port 7890
 * 2. Run: npm run test:proxy
 * 3. Look for "🎯 Proxy configuration is working correctly!" message
 *
 * SETUP REQUIREMENTS:
 * - Add these lines to your .env.local file:
 *   ENABLE_HTTP_PROXY=true
 *   HTTPS_PROXY=http://127.0.0.1:7890
 *
 * WHAT THIS TEST DOES:
 * ✅ Loads environment variables from .env.local automatically
 * ✅ Checks if proxy is enabled and configured
 * ✅ Tests connection to OpenAI API through your proxy
 * ✅ Shows clear success/failure messages with troubleshooting tips
 *
 * EXPECTED SUCCESS OUTPUT:
 * 🧪 Testing proxy configuration...
 * Environment variables:
 *   ENABLE_HTTP_PROXY: true
 *   HTTPS_PROXY: http://127.0.0.1:7890
 * 🌐 Testing connection through proxy: http://127.0.0.1:7890
 * ✅ Connection successful!
 *    Status: 401 Unauthorized
 *    ℹ️  Status 401 means proxy is working (API key required)
 *    🎯 Proxy configuration is working correctly!
 *
 * TROUBLESHOOTING COMMON ERRORS:
 *
 * 1. ETIMEDOUT Error:
 *    - Proxy server is slow or unresponsive
 *    - Solution: Restart your proxy software (Clash/V2Ray)
 *    - Check: Proxy software is actually running and accessible
 *
 * 2. ECONNREFUSED Error:
 *    - Proxy is not running or wrong port
 *    - Solution: Start Clash/V2Ray, verify port 7890 is correct
 *    - Test: Try accessing http://127.0.0.1:7890 in your browser
 *
 * 3. Environment Variables "undefined":
 *    - .env.local file missing or incorrect format
 *    - Solution: Create .env.local in project root with correct values
 *    - Check: File should contain ENABLE_HTTP_PROXY=true (no quotes)
 *
 * 4. "fetch is not a function" Error:
 *    - node-fetch import issue
 *    - Solution: This script uses node-fetch for better proxy support
 *    - Note: Node.js native fetch doesn't support proxy agents properly
 *
 * PROXY SOFTWARE SETUP:
 * - Clash: Usually runs on port 7890 by default
 * - V2Ray: May use port 10809, update HTTPS_PROXY if different
 * - Other: Check your proxy software's HTTP port setting
 *
 * NEXT STEPS AFTER SUCCESS:
 * 1. Run "npm run dev" to start the development server
 * 2. All AI API calls will automatically use the proxy
 * 3. Check browser console for any remaining connection issues
 * ============================================================================
 */

const fs = require('fs');
const path = require('path');
const { HttpsProxyAgent } = require('https-proxy-agent');

// Node.js v23.11.0 has native fetch, let's use it
// If not available, we'll handle it in the async function

// Load environment variables from .env.local
function loadEnvFile() {
  const envPath = path.join(__dirname, '.env.local');
  
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env.local file not found');
    process.exit(1);
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const lines = envContent.split('\n');

  lines.forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#') && line.includes('=')) {
      const [key, ...valueParts] = line.split('=');
      const value = valueParts.join('=').replace(/^["']|["']$/g, ''); // Remove quotes
      process.env[key] = value;
    }
  });
}

async function testProxy() {
  console.log('🧪 Testing proxy configuration...\n');

  // Use node-fetch for proper proxy support
  let fetchFunction;
  try {
    const nodeFetch = await import('node-fetch');
    fetchFunction = nodeFetch.default;
    console.log('✅ Using node-fetch library (better proxy support)\n');
  } catch (error) {
    console.error('❌ Cannot load node-fetch');
    console.error('💡 node-fetch is required for proxy support');
    console.error('💡 Run: npm install node-fetch');
    process.exit(1);
  }

  // Load environment variables
  loadEnvFile();

  // Check environment variables
  const enableProxy = process.env.ENABLE_HTTP_PROXY;
  const proxyUrl = process.env.HTTPS_PROXY;

  console.log('Environment variables:');
  console.log(`  ENABLE_HTTP_PROXY: ${enableProxy}`);
  console.log(`  HTTPS_PROXY: ${proxyUrl}\n`);

  if (enableProxy !== 'true') {
    console.log('❌ Proxy is disabled (ENABLE_HTTP_PROXY is not "true")');
    console.log('💡 Set ENABLE_HTTP_PROXY=true in .env.local to enable proxy');
    return;
  }

  if (!proxyUrl) {
    console.log('❌ No proxy URL configured');
    console.log('💡 Set HTTPS_PROXY=http://127.0.0.1:7890 in .env.local');
    return;
  }

  // Set up proxy
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
  const proxyAgent = new HttpsProxyAgent(proxyUrl);

  console.log(`🌐 Testing connection through proxy: ${proxyUrl}`);

  // First, test if proxy server is reachable
  console.log('🔍 Checking if proxy server is accessible...');
  try {
    const net = require('net');
    const socket = new net.Socket();

    await new Promise((resolve, reject) => {
      socket.setTimeout(3000);
      socket.on('connect', () => {
        console.log('✅ Proxy server is reachable');
        socket.destroy();
        resolve();
      });
      socket.on('timeout', () => {
        socket.destroy();
        reject(new Error('Proxy server connection timeout'));
      });
      socket.on('error', (err) => {
        socket.destroy();
        reject(err);
      });
      socket.connect(7890, '127.0.0.1');
    });
  } catch (error) {
    console.log('❌ Cannot connect to proxy server:');
    console.log(`   Error: ${error.message}`);
    console.log('\n💡 SOLUTION - Proxy Server Not Running:');
    console.log('   1. Start your proxy software (Clash/V2Ray/etc.)');
    console.log('   2. Make sure it\'s listening on port 7890');
    console.log('   3. Check proxy software settings');
    return;
  }

  console.log('🌐 Testing API connection through proxy...');
  try {
    const response = await fetchFunction('https://api.openai.com/v1/models', {
      agent: proxyAgent,
      headers: {
        'User-Agent': 'AI-Resume-Proxy-Test/1.0'
      },
      timeout: 10000 // 10 second timeout
    });

    console.log(`✅ Connection successful!`);
    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    if (response.status === 401) {
      console.log('   ℹ️  Status 401 means proxy is working (API key required)');
      console.log('   🎯 Proxy configuration is working correctly!');
    } else if (response.status === 200) {
      console.log('   🎯 Proxy configuration is working correctly!');
    } else {
      console.log(`   ⚠️  Unexpected status code: ${response.status}`);
    }

  } catch (error) {
    console.log('❌ API connection failed:');
    console.log(`   Error: ${error.message}`);
    console.log(`   Error code: ${error.code || 'Unknown'}`);

    if (error.message.includes('fetch failed')) {
      console.log('\n💡 SOLUTION - Fetch Failed:');
      console.log('   1. This usually means the proxy is not properly forwarding requests');
      console.log('   2. Check your proxy software configuration');
      console.log('   3. Make sure proxy allows HTTPS connections');
      console.log('   4. Try accessing https://api.openai.com directly in browser through proxy');
    } else if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 SOLUTION - Connection Refused:');
      console.log('   1. API server refused connection through proxy');
      console.log('   2. Check proxy software is properly configured');
      console.log('   3. Verify proxy can access external HTTPS sites');
    } else {
      console.log('\n💡 SOLUTION - General Error:');
      console.log('   1. Check proxy software logs for errors');
      console.log('   2. Try restarting proxy software');
      console.log('   3. Test proxy with browser first');
    }
  }
}

// Run the test
testProxy().catch(error => {
  console.error('Test failed:', error.message);
  process.exit(1);
});
