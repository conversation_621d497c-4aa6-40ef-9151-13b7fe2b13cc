/**
 * ============================================================================
 * PROXY TEST SCRIPT - AI Resume Project
 * ============================================================================
 *
 * QUICK START:
 * 1. Make sure your proxy software (Clash/V2Ray) is running on port 7890
 * 2. Run: npm run test:proxy
 * 3. Look for "🎯 Proxy configuration is working correctly!" message
 *
 * SETUP REQUIREMENTS:
 * - Add these lines to your .env.local file:
 *   ENABLE_HTTP_PROXY=true
 *   HTTPS_PROXY=http://127.0.0.1:7890
 *
 * WHAT THIS TEST DOES:
 * ✅ Loads environment variables from .env.local automatically
 * ✅ Checks if proxy is enabled and configured
 * ✅ Tests connection to OpenAI API through your proxy
 * ✅ Shows clear success/failure messages with troubleshooting tips
 *
 * EXPECTED SUCCESS OUTPUT:
 * 🧪 Testing proxy configuration...
 * Environment variables:
 *   ENABLE_HTTP_PROXY: true
 *   HTTPS_PROXY: http://127.0.0.1:7890
 * 🌐 Testing connection through proxy: http://127.0.0.1:7890
 * ✅ Connection successful!
 *    Status: 401 Unauthorized
 *    ℹ️  Status 401 means proxy is working (API key required)
 *    🎯 Proxy configuration is working correctly!
 *
 * TROUBLESHOOTING COMMON ERRORS:
 *
 * 1. ETIMEDOUT Error:
 *    - Proxy server is slow or unresponsive
 *    - Solution: Restart your proxy software (Clash/V2Ray)
 *    - Check: Proxy software is actually running and accessible
 *
 * 2. ECONNREFUSED Error:
 *    - Proxy is not running or wrong port
 *    - Solution: Start Clash/V2Ray, verify port 7890 is correct
 *    - Test: Try accessing http://127.0.0.1:7890 in your browser
 *
 * 3. Environment Variables "undefined":
 *    - .env.local file missing or incorrect format
 *    - Solution: Create .env.local in project root with correct values
 *    - Check: File should contain ENABLE_HTTP_PROXY=true (no quotes)
 *
 * 4. "fetch is not a function" Error:
 *    - Node.js version too old or node-fetch import issue
 *    - Solution: This script auto-detects and uses native fetch (Node 18+) or node-fetch
 *    - If still fails: Upgrade to Node.js 18+ for native fetch support
 *
 * PROXY SOFTWARE SETUP:
 * - Clash: Usually runs on port 7890 by default
 * - V2Ray: May use port 10809, update HTTPS_PROXY if different
 * - Other: Check your proxy software's HTTP port setting
 *
 * NEXT STEPS AFTER SUCCESS:
 * 1. Run "npm run dev" to start the development server
 * 2. All AI API calls will automatically use the proxy
 * 3. Check browser console for any remaining connection issues
 * ============================================================================
 */

const fs = require('fs');
const path = require('path');
const { HttpsProxyAgent } = require('https-proxy-agent');

// Node.js v23.11.0 has native fetch, let's use it
// If not available, we'll handle it in the async function

// Load environment variables from .env.local
function loadEnvFile() {
  const envPath = path.join(__dirname, '.env.local');
  
  if (!fs.existsSync(envPath)) {
    console.error('❌ .env.local file not found');
    process.exit(1);
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const lines = envContent.split('\n');

  lines.forEach(line => {
    line = line.trim();
    if (line && !line.startsWith('#') && line.includes('=')) {
      const [key, ...valueParts] = line.split('=');
      const value = valueParts.join('=').replace(/^["']|["']$/g, ''); // Remove quotes
      process.env[key] = value;
    }
  });
}

async function testProxy() {
  console.log('🧪 Testing proxy configuration...\n');

  // Check if native fetch is available (Node.js 18+)
  let fetchFunction;
  if (typeof fetch !== 'undefined') {
    fetchFunction = fetch;
    console.log('✅ Using native Node.js fetch\n');
  } else {
    // Try to import node-fetch as ES module
    try {
      const nodeFetch = await import('node-fetch');
      fetchFunction = nodeFetch.default;
      console.log('✅ Using node-fetch library\n');
    } catch (error) {
      console.error('❌ Cannot load fetch function');
      console.error('💡 Please upgrade to Node.js 18+ or install node-fetch');
      process.exit(1);
    }
  }

  // Load environment variables
  loadEnvFile();

  // Check environment variables
  const enableProxy = process.env.ENABLE_HTTP_PROXY;
  const proxyUrl = process.env.HTTPS_PROXY;

  console.log('Environment variables:');
  console.log(`  ENABLE_HTTP_PROXY: ${enableProxy}`);
  console.log(`  HTTPS_PROXY: ${proxyUrl}\n`);

  if (enableProxy !== 'true') {
    console.log('❌ Proxy is disabled (ENABLE_HTTP_PROXY is not "true")');
    console.log('💡 Set ENABLE_HTTP_PROXY=true in .env.local to enable proxy');
    return;
  }

  if (!proxyUrl) {
    console.log('❌ No proxy URL configured');
    console.log('💡 Set HTTPS_PROXY=http://127.0.0.1:7890 in .env.local');
    return;
  }

  // Set up proxy
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
  const proxyAgent = new HttpsProxyAgent(proxyUrl);

  console.log(`🌐 Testing connection through proxy: ${proxyUrl}`);

  try {
    const response = await fetchFunction('https://api.openai.com/v1/models', {
      agent: proxyAgent,
      headers: {
        'User-Agent': 'AI-Resume-Proxy-Test/1.0'
      },
      timeout: 10000 // 10 second timeout
    });

    console.log(`✅ Connection successful!`);
    console.log(`   Status: ${response.status} ${response.statusText}`);
    
    if (response.status === 401) {
      console.log('   ℹ️  Status 401 means proxy is working (API key required)');
      console.log('   🎯 Proxy configuration is working correctly!');
    } else if (response.status === 200) {
      console.log('   🎯 Proxy configuration is working correctly!');
    } else {
      console.log(`   ⚠️  Unexpected status code: ${response.status}`);
    }

  } catch (error) {
    console.log('❌ Connection failed:');
    console.log(`   Error: ${error.message}`);

    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 SOLUTION - Proxy Not Running:');
      console.log('   1. Start your proxy software (Clash/V2Ray/etc.)');
      console.log('   2. Verify it\'s listening on port 7890');
      console.log('   3. Test: Open http://127.0.0.1:7890 in browser');
      console.log('   4. If different port, update HTTPS_PROXY in .env.local');
    } else if (error.code === 'ETIMEDOUT') {
      console.log('\n💡 SOLUTION - Connection Timeout:');
      console.log('   1. Restart your proxy software');
      console.log('   2. Check proxy software is responding');
      console.log('   3. Try different proxy server if available');
      console.log('   4. Check firewall isn\'t blocking the connection');
    } else if (error.code === 'ENOTFOUND') {
      console.log('\n💡 SOLUTION - Network Issue:');
      console.log('   1. Check your internet connection');
      console.log('   2. Verify proxy server address is correct');
      console.log('   3. Try without proxy to test basic connectivity');
    } else {
      console.log('\n💡 SOLUTION - Unknown Error:');
      console.log('   1. Check all proxy software is running');
      console.log('   2. Verify .env.local configuration');
      console.log('   3. Try restarting proxy software');
    }
  }
}

// Run the test
testProxy().catch(error => {
  console.error('Test failed:', error.message);
  process.exit(1);
});
